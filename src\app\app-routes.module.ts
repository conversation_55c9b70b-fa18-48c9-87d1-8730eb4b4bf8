import { RouterModule, Routes } from '@angular/router';
import { DashboardComponent } from './components/dashboard/dashboard.component';
import { NgModule } from '@angular/core';
import { FixturesComponent } from './components/fixtures/fixtures.component';
import { LeagueTableComponent } from './components/league-table/league-table.component';
import { TopScorersComponent } from './components/top-scorers/top-scorers.component';
import { TopAssistsComponent } from './components/top-assists/top-assists.component';
import { TeamDetailsComponent } from './components/team-details/team-details.component';
import { PlayerDetailsComponent } from './components/player-details/player-details.component';
import { AddFixtureComponent } from './components/add-fixture/add-fixture.component';
import { SignUpComponent } from './components/sign-up/sign-up.component';
import { LoginComponent } from './components/login/login.component';
import { CreatePlayerComponent } from './components/create-player/create-player.component';
import { AssignPlayerToTeamComponent } from './components/assign-player-to-team/assign-player-to-team.component';
import { CreateTeamComponent } from './components/create-team/create-team.component';
import { TotwComponent } from './components/totw/totw.component';
import { TopAvgRatingByPositionComponent } from './components/top-avg-rating-by-position/top-avg-rating-by-position.component';
import { RulesComponent } from './components/rules/rules.component';
import { NewsComponent } from './components/news/news.component';
import { AddNewsComponent } from './components/add-news/add-news.component';
import { ChatComponent } from './components/chat/chat.component';
import { TransferRequestsComponent } from './components/transfer-requests/transfer-requests.component';

import { PlayerSearchComponent } from './components/player-search/player-search.component';
import { PlayerComparisonComponent } from './components/player-comparison/player-comparison.component';
import { PlayerSeasonHistoryComponent } from './components/player-season-history/player-season-history.component';
import { UserProfileComponent } from './components/user-profile/user-profile.component';
import { AdminPlayerRequestsComponent } from './components/admin-player-requests/admin-player-requests.component';
import { AdminTeamLeagueManagementComponent } from './components/admin-team-league-management/admin-team-league-management.component';
import { AdminCmsDashboardComponent } from './components/admin-cms-dashboard/admin-cms-dashboard.component';
import { AdminEndSeasonComponent } from './components/admin-end-season/admin-end-season.component';
import { AllTimeStatsComponent } from './components/all-time-stats/all-time-stats.component';
import { AiImportComponent } from './components/ai-import/ai-import.component';
import { MiniPitchTestComponent } from './components/mini-pitch-test/mini-pitch-test.component';
import { AuthGuard } from './guards/auth.guard';
import { AdminGuard } from './guards/admin.guard';
import { GameDetailsComponent } from './components/game-details/game-details.component';
import { ModifyGameComponent } from './components/modify-game/modify-game.component';


const routes: Routes = [
    {
        path: '', redirectTo: 'sign-up', pathMatch: 'full'
    },
    {
        path: 'dashboard',
        component: DashboardComponent
    },
    {
        path: 'fixtures',
        component: FixturesComponent
    },

    {
        path: 'top-assists',
        component: TopAssistsComponent
    },
    {
        path: 'top-scorers',
        component: TopScorersComponent
    },
    {
        path: 'top-avg-rating-by-position',
        component: TopAvgRatingByPositionComponent
    },
    {
        path: 'all-time-stats',
        component: AllTimeStatsComponent
    },
    {
        path: 'league-table',
        component: LeagueTableComponent
    },
    {
        path: 'team-details',
        component: TeamDetailsComponent
    },
    {
        path: 'team-details/:id',
        component: TeamDetailsComponent
    },
    {
        path: 'player-details',
        component: PlayerDetailsComponent
    },
    {
        path: 'player-comparison/:player1Id',
        component: PlayerComparisonComponent
    },
    {
        path: 'player-comparison/:player1Id/:player2Id',
        component: PlayerComparisonComponent
    },
    {
        path: 'player-season-history/:playerId',
        component: PlayerSeasonHistoryComponent
    },
    {
        path: 'create-team',
        component: CreateTeamComponent,
        canActivate: [AuthGuard, AdminGuard]
    },
    {
        path: 'create-player',
        component: CreatePlayerComponent,
        canActivate: [AuthGuard, AdminGuard]
    },
    {
        path: 'add-fixture',
        component: AddFixtureComponent,
        canActivate: [AuthGuard, AdminGuard]
    },
    {
        path: 'sign-up',
        component: SignUpComponent
    },
    {
        path: 'login',
        component: LoginComponent
    },
    {
        path: 'assign-player-to-team',
        component: AssignPlayerToTeamComponent,
        canActivate: [AuthGuard, AdminGuard]
    },
    {
        path: 'totw',
        component: TotwComponent
    },
    {
        path: 'news',
        component: NewsComponent
    },
    {
        path: 'chat',
        component: ChatComponent,
        canActivate: [AuthGuard]
    },
    {
        path: 'transfer-requests',
        component: TransferRequestsComponent,
        canActivate: [AuthGuard]
    },

    {
        path: 'rules',
        component: RulesComponent
    },
    {
        path: 'add-news',
        component: AddNewsComponent,
        canActivate: [AuthGuard, AdminGuard]
    },
    {
        path: 'admin/player-requests',
        component: AdminPlayerRequestsComponent,
        canActivate: [AuthGuard, AdminGuard]
    },
    {
        path: 'admin/team-league-management',
        component: AdminTeamLeagueManagementComponent,
        canActivate: [AuthGuard, AdminGuard]
    },
    {
        path: 'admin/cms-dashboard',
        component: AdminCmsDashboardComponent,
        canActivate: [AuthGuard, AdminGuard]
    },
    {
        path: 'admin/end-season',
        component: AdminEndSeasonComponent,
        canActivate: [AuthGuard, AdminGuard]
    },
    {
        path: 'player-search',
        component: PlayerSearchComponent
    },
    {
        path: 'profile',
        component: UserProfileComponent,
        canActivate: [AuthGuard]
    },
    {
        path: 'player/:id',
        component: PlayerDetailsComponent
    },
    {
        path: 'ai-import',
        component: AiImportComponent,
        canActivate: [AuthGuard, AdminGuard]
    },
    {
        path: 'mini-pitch-test',
        component: MiniPitchTestComponent
    },
    {
        path: 'game-details/:id',
        component: GameDetailsComponent
    },
    {
        path: 'modify-game/:id',
        component: ModifyGameComponent,
        canActivate: [AuthGuard]
    },
];

@NgModule({
    imports: [RouterModule.forRoot(routes)],
    exports: [RouterModule],
    providers: []
})
export class AppRoutingModule { }