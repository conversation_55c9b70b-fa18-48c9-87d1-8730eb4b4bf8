const mongoose = require('mongoose');
const { Types } = mongoose;

// Achievement types enum
const ACHIEVEMENT_TYPE = {
  CHAMPIONSHIP_WINNER: "Championship Winner",
  FINALIST: "Finalist", 
  THIRD_PLACE: "Third Place",
  TOP_SCORER: "Top Scorer",
  TOP_ASSIST_PROVIDER: "Top Assist Provider",
  BEST_GOALKEEPER: "Best Goalkeeper",
  BEST_CENTER_BACK: "Best Center Back",
  BEST_DEFENSIVE_MIDFIELDER: "Best Defensive Midfielder",
  BEST_MIDFIELDER: "Best Midfielder",
  BEST_ATTACKING_MIDFIELDER: "Best Attacking Midfielder",
  BEST_WINGER: "Best Winger",
  BEST_STRIKER: "Best Striker",
  MOST_CLEAN_SHEETS: "Most Clean Sheets",
  PLAYER_OF_THE_SEASON: "Player of the Season",
  TEAM_OF_THE_SEASON: "Team of the Season"
};

// Connect to MongoDB
async function connectToDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/proclubsstats');
    console.log('Connected to MongoDB');
  } catch (error) {
    console.error('Error connecting to MongoDB:', error);
    process.exit(1);
  }
}

// Define schemas (simplified versions)
const playerSchema = new mongoose.Schema({
  name: String,
  achievementHistory: [{
    seasonNumber: Number,
    league: { type: mongoose.Schema.Types.ObjectId, ref: 'League' },
    leagueName: String,
    achievementType: String,
    rank: Number,
    teamId: { type: mongoose.Schema.Types.ObjectId, ref: 'Team' },
    teamName: String,
    stats: {
      goals: Number,
      assists: Number,
      cleanSheets: Number,
      avgRating: Number,
      games: Number,
      playerOfTheMatch: Number,
    },
    description: String,
    achievedDate: Date
  }]
}, { collection: 'players' });

const teamSchema = new mongoose.Schema({
  name: String,
  league: { type: mongoose.Schema.Types.ObjectId, ref: 'League' },
  achievementHistory: [{
    seasonNumber: Number,
    league: { type: mongoose.Schema.Types.ObjectId, ref: 'League' },
    leagueName: String,
    achievementType: String,
    rank: Number,
    stats: {
      wins: Number,
      losses: Number,
      draws: Number,
      goalsScored: Number,
      goalsConceded: Number,
      points: Number,
      goalDifference: Number,
    },
    description: String,
    achievedDate: Date
  }]
}, { collection: 'teams' });

const leagueSchema = new mongoose.Schema({
  name: String
}, { collection: 'leagues' });

const Player = mongoose.model('Player', playerSchema);
const Team = mongoose.model('Team', teamSchema);
const League = mongoose.model('League', leagueSchema);

// Team achievements to add
const teamAchievements = [
  {
    teamId: '678907060ac8f44728a5e0dc',
    achievements: [
      { seasonNumber: 4, achievementType: ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER, rank: 1, description: 'Season 4 Champion' },
      { seasonNumber: 5, achievementType: ACHIEVEMENT_TYPE.FINALIST, rank: 2, description: 'Season 5 Runner-up' }
    ]
  },
  {
    teamId: '66058b5119a6c5698f4ba74b',
    achievements: [
      { seasonNumber: 1, achievementType: ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER, rank: 1, description: 'Season 1 Champion' },
      { seasonNumber: 2, achievementType: ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER, rank: 1, description: 'Season 2 Champion' },
      { seasonNumber: 3, achievementType: ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER, rank: 1, description: 'Season 3 Champion' },
      { seasonNumber: 4, achievementType: ACHIEVEMENT_TYPE.FINALIST, rank: 2, description: 'Season 4 Runner-up' },
      { seasonNumber: 5, achievementType: ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER, rank: 1, description: 'Season 5 Champion' }
    ]
  }
];

// Player achievements to add
const playerAchievements = [
  {
    playerId: '6654f375f9eac0fe69961bfd',
    achievements: [
      { seasonNumber: 2, achievementType: ACHIEVEMENT_TYPE.TOP_SCORER, rank: 1, description: 'Season 2: 1st in top scorers' },
      { seasonNumber: 3, achievementType: ACHIEVEMENT_TYPE.TOP_SCORER, rank: 2, description: 'Season 3: 2nd in top scorers' }
    ]
  },
  {
    playerId: '6617944dd6085929ce96da8c',
    achievements: [
      { seasonNumber: 2, achievementType: ACHIEVEMENT_TYPE.TOP_SCORER, rank: 2, description: 'Season 2: 2nd in top scorers' }
    ]
  },
  {
    playerId: '660a712a0381fc57697ec9fe',
    achievements: [
      { seasonNumber: 3, achievementType: ACHIEVEMENT_TYPE.TOP_SCORER, rank: 3, description: 'Season 3: 3rd in top scorers' }
    ]
  },
  {
    playerId: '660bbdbb22901e293800b2f3',
    achievements: [
      { seasonNumber: 2, achievementType: ACHIEVEMENT_TYPE.TOP_ASSIST_PROVIDER, rank: 1, description: 'Season 2: 1st in top assists' },
      { seasonNumber: 3, achievementType: ACHIEVEMENT_TYPE.TOP_ASSIST_PROVIDER, rank: 3, description: 'Season 3: 3rd in top assists' }
    ]
  },
  {
    playerId: '6624e5ae2e2d3dd16735c1fa',
    achievements: [
      { seasonNumber: 3, achievementType: ACHIEVEMENT_TYPE.TOP_SCORER, rank: 1, description: 'Season 3: 1st in top scorers' }
    ]
  }
];

async function addTeamAchievements() {
  console.log('Adding team achievements...');

  for (const teamData of teamAchievements) {
    try {
      const team = await Team.findById(teamData.teamId).populate('league');
      if (!team) {
        console.error(`Team with ID ${teamData.teamId} not found`);
        continue;
      }

      console.log(`Processing team: ${team.name}`);

      // Get league information
      let leagueId = team.league?._id || team.league;
      let leagueName = team.league?.name || 'Pro Clubs League';

      // If no league is set, try to find the first league
      if (!leagueId) {
        const firstLeague = await League.findOne();
        if (firstLeague) {
          leagueId = firstLeague._id;
          leagueName = firstLeague.name;
        }
      }

      for (const achievement of teamData.achievements) {
        // Check if achievement already exists
        const existingAchievement = team.achievementHistory.find(a =>
          a.seasonNumber === achievement.seasonNumber &&
          a.achievementType === achievement.achievementType
        );

        if (existingAchievement) {
          console.log(`  - Achievement already exists: ${achievement.description}`);
          continue;
        }

        team.achievementHistory.push({
          seasonNumber: achievement.seasonNumber,
          league: leagueId,
          leagueName: leagueName,
          achievementType: achievement.achievementType,
          rank: achievement.rank,
          stats: {}, // Add actual stats if available
          description: achievement.description,
          achievedDate: new Date()
        });

        console.log(`  + Added: ${achievement.description}`);
      }

      await team.save();
      console.log(`✓ Saved achievements for team: ${team.name}`);

    } catch (error) {
      console.error(`Error processing team ${teamData.teamId}:`, error);
    }
  }
}

async function addPlayerAchievements() {
  console.log('\nAdding player achievements...');

  for (const playerData of playerAchievements) {
    try {
      const player = await Player.findById(playerData.playerId);
      if (!player) {
        console.error(`Player with ID ${playerData.playerId} not found`);
        continue;
      }

      console.log(`Processing player: ${player.name}`);

      // Get player's current team and league information
      let teamId = player.currentSeason?.team;
      let teamName = 'Unknown Team';
      let leagueId = null;
      let leagueName = 'Pro Clubs League';

      if (teamId) {
        const team = await Team.findById(teamId).populate('league');
        if (team) {
          teamName = team.name;
          leagueId = team.league?._id || team.league;
          leagueName = team.league?.name || 'Pro Clubs League';
        }
      }

      // If no league found, get the first available league
      if (!leagueId) {
        const firstLeague = await League.findOne();
        if (firstLeague) {
          leagueId = firstLeague._id;
          leagueName = firstLeague.name;
        }
      }

      for (const achievement of playerData.achievements) {
        // Check if achievement already exists
        const existingAchievement = player.achievementHistory.find(a =>
          a.seasonNumber === achievement.seasonNumber &&
          a.achievementType === achievement.achievementType
        );

        if (existingAchievement) {
          console.log(`  - Achievement already exists: ${achievement.description}`);
          continue;
        }

        player.achievementHistory.push({
          seasonNumber: achievement.seasonNumber,
          league: leagueId,
          leagueName: leagueName,
          achievementType: achievement.achievementType,
          rank: achievement.rank,
          teamId: teamId,
          teamName: teamName,
          stats: {}, // Add actual stats if available
          description: achievement.description,
          achievedDate: new Date()
        });

        console.log(`  + Added: ${achievement.description}`);
      }

      await player.save();
      console.log(`✓ Saved achievements for player: ${player.name}`);

    } catch (error) {
      console.error(`Error processing player ${playerData.playerId}:`, error);
    }
  }
}

async function main() {
  try {
    await connectToDatabase();
    
    await addTeamAchievements();
    await addPlayerAchievements();
    
    console.log('\n✅ All achievements have been added successfully!');
    
  } catch (error) {
    console.error('Error in main process:', error);
  } finally {
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
}

// Run the script
main();
