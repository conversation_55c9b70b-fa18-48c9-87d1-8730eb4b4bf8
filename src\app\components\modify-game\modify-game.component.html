<div class="view-only-message" *ngIf="!(canAccessModifyGame() | async)">
    <div class="message-content">
        <i class="fas fa-lock"></i>
        <h2>Access Required</h2>
        <p>Game modification requires admin privileges or team captain status.</p>
    </div>
</div>

<div class="modify-game-container" *ngIf="!isLoading && selectedGame && (canAccessModifyGame() | async)">
    <div class="match-header">
        <div class="match-title">
            <i class="fas fa-edit"></i>
            <h1>Modify Game</h1>
        </div>

        <div class="score-section" >
            <div class="team-section home-team" *ngIf="selectedGame.homeTeam">
                <div class="team-info">
                    <img class="team-logo" [src]="selectedGame.homeTeam.imgUrl || 'assets/Icons/TeamLogo.jpg'"
                        [alt]="selectedGame.homeTeam.name" />
                    <div class="team-details">
                        <h3 class="team-name">{{selectedGame.homeTeam.name}}</h3>
                        <span class="team-label">Home</span>
                    </div>
                </div>
            </div>

            <div class="score-input-section" *ngIf="isAdminUser() | async">
                <div class="score-inputs">
                    <div class="score-input-group">
                        <input type="number" min="0" max="99" [(ngModel)]="homeTeamGoalsAmount"
                            class="score-input home-score" placeholder="0">
                        <label class="score-label">Home Goals</label>
                    </div>
                    <div class="score-separator"><span class="vs-text">VS</span></div>
                    <div class="score-input-group">
                        <input type="number" min="0" max="99" [(ngModel)]="awayTeamGoalsAmount"
                            class="score-input away-score" placeholder="0">
                        <label class="score-label">Away Goals</label>
                    </div>
                </div>

                <!-- Penalty Shootout Section -->
                <div class="penalty-section" *ngIf="shouldShowPenalties()">
                    <div class="penalty-header">
                        <i class="fas fa-futbol"></i>
                        <h4>Penalty Shootout</h4>
                        <span class="penalty-info">(Required for playoff draws)</span>
                    </div>
                    <div class="penalty-inputs">
                        <div class="penalty-input-group">
                            <input type="number" min="0" max="20" [(ngModel)]="homeTeamPenalties"
                                class="penalty-input home-penalties" placeholder="0">
                            <label class="penalty-label">Home Penalties</label>
                        </div>
                        <div class="penalty-separator"><span class="penalty-text">PENS</span></div>
                        <div class="penalty-input-group">
                            <input type="number" min="0" max="20" [(ngModel)]="awayTeamPenalties"
                                class="penalty-input away-penalties" placeholder="0">
                            <label class="penalty-label">Away Penalties</label>
                        </div>
                    </div>
                    <div class="penalty-validation" *ngIf="homeTeamPenalties === awayTeamPenalties && homeTeamPenalties > 0">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>Penalty shootout cannot end in a draw. One team must win.</span>
                    </div>
                </div>
            </div>

            <!-- Score display for non-admin users -->
            <div class="score-display-section" *ngIf="!(isAdminUser() | async)">
                <div class="score-display">
                    <div class="score-display-group">
                        <span class="score-display-value">{{homeTeamGoalsAmount}}</span>
                        <label class="score-label">Home Goals</label>
                    </div>
                    <div class="score-separator"><span class="vs-text">VS</span></div>
                    <div class="score-display-group">
                        <span class="score-display-value">{{awayTeamGoalsAmount}}</span>
                        <label class="score-label">Away Goals</label>
                    </div>
                </div>
            </div>

            <div class="team-section away-team" *ngIf="selectedGame.awayTeam">
                <div class="team-info">
                    <div class="team-details">
                        <h3 class="team-name">{{selectedGame.awayTeam.name}}</h3>
                        <span class="team-label">Away</span>
                    </div>
                    <img class="team-logo" [src]="selectedGame.awayTeam.imgUrl || 'assets/Icons/TeamLogo.jpg'"
                        [alt]="selectedGame.awayTeam.name" />
                </div>
            </div>
        </div>

        <div class="technical-result-section" *ngIf="isAdminUser() | async">
            <div class="technical-header">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>Technical Result</h3>
            </div>
            <div class="technical-content">
                <div class="technical-info">
                    <p class="technical-description">Set a technical result when a team breaks rules. The offending team
                        will automatically lose 0-3.</p>
                </div>
                <div class="technical-controls">
                    <div class="team-selection">
                        <label class="technical-label">Select losing team:</label>
                        <div class="team-buttons">
                            <button type="button" class="team-btn home-btn"
                                [class.selected]="technicalLosingTeam === 'home'"
                                (click)="selectTechnicalLosingTeam('home')">
                                <img [src]="selectedGame!.homeTeam!.imgUrl || 'assets/Icons/TeamLogo.jpg'"
                                    [alt]="selectedGame!.homeTeam!.name">
                                <span>{{selectedGame!.homeTeam!.name}}</span>
                            </button>
                            <button type="button" class="team-btn away-btn"
                                [class.selected]="technicalLosingTeam === 'away'"
                                (click)="selectTechnicalLosingTeam('away')">
                                <img [src]="selectedGame!.awayTeam!.imgUrl || 'assets/Icons/TeamLogo.jpg'"
                                    [alt]="selectedGame!.awayTeam!.name">
                                <span>{{selectedGame!.awayTeam!.name}}</span>
                            </button>
                        </div>
                    </div>
                    <div class="reason-input">
                        <label class="technical-label">Reason for technical result:</label>
                        <textarea [(ngModel)]="technicalReason" class="reason-textarea"
                            placeholder="Enter the reason for the technical result" rows="3"></textarea>
                    </div>
                    <div class="technical-actions">
                        <button type="button" class="technical-btn apply-btn"
                            [disabled]="!technicalLosingTeam || !technicalReason.trim()"
                            (click)="applyTechnicalResult()">
                            <i class="fas fa-gavel"></i>Apply Technical Result (0-3)
                        </button>
                        <button type="button" class="technical-btn clear-btn" *ngIf="technicalLosingTeam"
                            (click)="clearTechnicalResult()">
                            <i class="fas fa-times"></i>Clear Selection
                        </button>
                    </div>
                </div>
                <div class="technical-preview" *ngIf="technicalLosingTeam">
                    <div class="preview-header">
                        <i class="fas fa-eye"></i>
                        <span>Result Preview:</span>
                    </div>
                    <div class="preview-score">
                        <span class="preview-team"
                            [class.losing]="technicalLosingTeam === 'home'">{{selectedGame!.homeTeam!.name}}:
                            {{technicalLosingTeam === 'home' ? '0' : '3'}}</span>
                        <span class="preview-separator">-</span>
                        <span class="preview-team"
                            [class.losing]="technicalLosingTeam === 'away'">{{selectedGame!.awayTeam!.name}}:
                            {{technicalLosingTeam === 'away' ? '0' : '3'}}</span>
                    </div>
                    <div class="preview-reason"><strong>Reason:</strong> {{technicalReason}}</div>
                </div>
            </div>
        </div>

        <div class="datetime-section" *ngIf="isAdminUser() | async">
            <div class="datetime-header">
                <i class="fas fa-calendar-alt"></i>
                <h3>Game Date & Time</h3>
            </div>
            <div class="datetime-inputs">
                <div class="datetime-input-group">
                    <label class="datetime-label">Date</label>
                    <input type="date" [(ngModel)]="gameDateISO" class="datetime-input date-input"
                        [class.modified]="isDateModified()">
                </div>
                <div class="datetime-input-group">
                    <label class="datetime-label">Time (24hr)</label>
                    <input type="time" [(ngModel)]="gameTime" class="datetime-input time-input" step="60"
                        [class.modified]="isDateModified()">
                </div>
            </div>
            <div class="datetime-info" *ngIf="isDateModified()">
                <i class="fas fa-info-circle"></i>
                <span>Date/time has been modified and will be updated when you save</span>
            </div>
        </div>

        <div class="broadcast-section" *ngIf="selectedGame?.status === GameStatus.SCHEDULED && (isAdminUser() | async)">
            <div class="broadcast-header">
                <i class="fas fa-broadcast-tower"></i>
                <h3>Live Broadcast</h3>
            </div>
            <div class="broadcast-inputs">
                <div class="broadcast-input-group">
                    <label class="broadcast-label">Stream URL</label>
                    <input type="url" [(ngModel)]="streamUrl" class="broadcast-input url-input"
                        [class.modified]="isBroadcastModified()" placeholder="https://twitch.tv/your-channel">
                </div>
                <div class="broadcast-input-group">
                    <label class="broadcast-label">Broadcasting Team</label>
                    <input type="text" [(ngModel)]="broadcastingTeam" class="broadcast-input team-input"
                        [class.modified]="isBroadcastModified()" placeholder="Team providing the stream">
                </div>
            </div>
            <div class="broadcast-info" *ngIf="isBroadcastModified()">
                <i class="fas fa-info-circle"></i>
                <span>Broadcast information has been modified and will be updated when you save</span>
            </div>
            <div class="broadcast-help">
                <i class="fas fa-question-circle"></i>
                <span>Add live stream information for upcoming matches. This will be displayed to viewers.</span>
            </div>
        </div>
    </div>

    <div class="players-section">
        <div class="section-header">
            <h2 class="section-title">
                <i class="fas fa-users"></i>Player Statistics
                <!-- Show team name for captains -->
                <span *ngIf="!(isAdminUser() | async) && team === 'home' && selectedGame?.homeTeam" class="team-indicator">- {{selectedGame.homeTeam.name}}</span>
                <span *ngIf="!(isAdminUser() | async) && team === 'away' && selectedGame?.awayTeam" class="team-indicator">- {{selectedGame.awayTeam.name}}</span>
            </h2>
            <p class="section-subtitle" *ngIf="isAdminUser() | async">Update player performance data for this match</p>
            <p class="section-subtitle" *ngIf="!(isAdminUser() | async)">Update your team's player performance data for this match</p>
        </div>

        <div class="ai-upload-section">
            <div class="upload-container">
                <div class="upload-header">
                    <i class="fas fa-robot"></i>
                    <h3>AI-Powered Data Entry</h3>
                    <p>Upload a FIFA Pro Clubs screenshot to automatically extract player data</p>
                </div>

                <div class="upload-area" [class.has-image]="imagePreviewUrl" [class.processing]="isProcessingImage">
                    <input type="file" #fileInput accept="image/*" (change)="onFileSelected($event)"
                        style="display: none;">

                    <div class="upload-zone" *ngIf="!imagePreviewUrl" (click)="fileInput.click()">
                        <div class="upload-content">
                            <i class="fas fa-cloud-upload-alt upload-icon"></i>
                            <h4>Upload Screenshot</h4>
                            <p>Click to select or drag & drop your FIFA Pro Clubs screenshot</p>
                            <span class="file-types">Supports: JPG, PNG, WebP (Max 10MB)</span>
                        </div>
                    </div>

                    <div class="image-preview" *ngIf="imagePreviewUrl">
                        <img [src]="imagePreviewUrl" alt="Screenshot preview" class="preview-image">
                        <div class="preview-overlay">
                            <div class="preview-actions">
                                <button type="button" class="action-btn retry-btn" (click)="fileInput.click()"
                                    [disabled]="isProcessingImage">
                                    <i class="fas fa-redo"></i><span>Change Image</span>
                                </button>
                                <button type="button" class="action-btn clear-btn" (click)="clearSelectedImage()"
                                    [disabled]="isProcessingImage">
                                    <i class="fas fa-times"></i><span>Clear</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="processing-overlay" *ngIf="isProcessingImage">
                        <div class="processing-content">
                            <div class="spinner"></div>
                            <h4>Analyzing Image...</h4>
                            <p>AI is extracting player data from your screenshot</p>
                        </div>
                    </div>
                </div>

                <div class="upload-tips">
                    <h4><i class="fas fa-lightbulb"></i> Tips for best results:</h4>
                    <ul>
                        <li>Use clear, high-resolution screenshots</li>
                        <li>Ensure all player names and stats are visible</li>
                        <li>Avoid screenshots with overlays or menus</li>
                        <li>The AI will automatically match players to your team roster</li>
                    </ul>
                </div>
            </div>
        </div>

        <form [formGroup]="playersStatsFormGroup!" (ngSubmit)="submitForm()" class="players-form">
            <div formArrayName="playersToUpdate" class="players-container">
                <!-- Players Grid -->
                <div class="players-grid">
                    <div class="player-cube" *ngFor="let player of playersToUpdate.controls; let i = index"
                        [formGroupName]="i">

                        <!-- Player Selection -->
                        <div class="cube-section player-section">
                            <div class="section-header">
                                <i class="fas fa-user section-icon"></i>
                                <span class="section-label">Player</span>
                            </div>
                            <pro-clubs-auto-complete-select
                                [defaultOption]="player.get('playerId')!.value"
                                [placeholder]="'Select Player'"
                                (selectionChange)="onSelectionChange('playerId', $event, i)"
                                [selectOptions]="playersOptions!"
                                class="cube-select player-select">
                            </pro-clubs-auto-complete-select>
                        </div>

                        <!-- Position Selection -->
                        <div class="cube-section position-section">
                            <div class="section-header">
                                <i class="fas fa-map-marker-alt section-icon"></i>
                                <span class="section-label">Position</span>
                            </div>
                            <pro-clubs-auto-complete-select
                                [placeholder]="'Position'"
                                [defaultOption]="player.get('positionPlayed') ? player.get('positionPlayed')!.value : ''"
                                (selectionChange)="onSelectionChange('position', $event, i)"
                                [selectOptions]="playablePositionOptions"
                                class="cube-select position-select">
                            </pro-clubs-auto-complete-select>
                        </div>

                        <!-- Stats Section -->
                        <div class="cube-section stats-section">
                            <div class="stats-grid">
                                <div class="stat-item">
                                    <label class="stat-label">
                                        <i class="fas fa-star"></i>
                                        <span>Rating</span>
                                    </label>
                                    <input type="number" formControlName="rating"
                                           class="cube-input rating-input"
                                           min="0" max="10" step="0.1" placeholder="0.0">
                                </div>
                                <div class="stat-item">
                                    <label class="stat-label">
                                        <i class="fas fa-futbol"></i>
                                        <span>Goals</span>
                                    </label>
                                    <input type="number" formControlName="goals"
                                           class="cube-input goals-input"
                                           min="0" max="99" placeholder="0">
                                </div>
                                <div class="stat-item">
                                    <label class="stat-label">
                                        <i class="fas fa-hands-helping"></i>
                                        <span>Assists</span>
                                    </label>
                                    <input type="number" formControlName="assists"
                                           class="cube-input assists-input"
                                           min="0" max="99" placeholder="0">
                                </div>
                            </div>
                        </div>

                        <!-- Actions Section -->
                        <div class="cube-section actions-section">
                            <div class="actions-grid">
                                <label class="potm-container">
                                    <input type="checkbox" formControlName="playerOfTheMatch"
                                           (change)="onCheckboxCheck(i)" class="potm-checkbox">
                                    <span class="potm-custom">
                                        <i class="fas fa-crown"></i>
                                        <span class="potm-label">POTM</span>
                                    </span>
                                </label>
                                <button type="button" class="remove-cube-button"
                                        (click)="removePlayer(i)" title="Remove Player">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="controls-section">
                    <div class="controls-container">
                        <div class="control-group">
                            <button type="button" class="control-button add-player-btn"
                                *ngIf="playersToUpdate.length < 11" (click)="addPlayer()">
                                <i class="fas fa-user-plus"></i><span>Add Player</span>
                            </button>
                            <button type="button" class="control-button add-team-btn"
                                *ngIf="playersToUpdate.length < 11" (click)="addFullTeam()">
                                <i class="fas fa-users"></i><span>Add Full Team</span>
                            </button>
                            <button type="button" class="control-button remove-all-btn"
                                *ngIf="playersToUpdate.length > 0" (click)="removeAllPlayers()">
                                <i class="fas fa-trash-alt"></i><span>Remove All</span>
                            </button>
                        </div>

                        <div class="formation-group" *ngIf="formationsOptions">
                            <label class="formation-label"><i class="fas fa-chess-board"></i>Formation</label>
                            <pro-clubs-auto-complete-select [placeholder]="'Select Formation'" class="formation-select"
                                (selectionChange)="onFormationChoose($event)"
                                [selectOptions]="formationsOptions"></pro-clubs-auto-complete-select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="save-section">
                <button type="submit" class="save-button" [disabled]="isSaving">
                    <i class="fas" [class.fa-save]="!isSaving" [class.fa-spinner]="isSaving" [class.fa-spin]="isSaving"></i>
                    <span>{{ isSaving ? 'Saving...' : 'Save Changes' }}</span>
                </button>
            </div>
        </form>
    </div>
</div>

<pro-clubs-spinner *ngIf="isLoading"></pro-clubs-spinner>