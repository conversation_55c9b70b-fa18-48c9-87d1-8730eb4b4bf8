# Achievement Scripts

This directory contains scripts for manually adding achievements to teams and players.

## Adding Manual Achievements

### Prerequisites

1. Make sure you're in the server directory: `cd ProClubsStatsServer`
2. Ensure your MongoDB connection is configured properly in your environment variables
3. Make sure all dependencies are installed: `npm install`

### Running the Achievement Script

To add the predefined achievements for teams and players, run:

```bash
npm run add-achievements
```

### What the Script Does

The script will add the following achievements:

#### Team Achievements

**Team ID: 678907060ac8f44728a5e0dc**
- Season 4: Champion
- Season 5: Runner-up

**Team ID: 66058b5119a6c5698f4ba74b**
- Season 1: Champion
- Season 2: Champion
- Season 3: Champion
- Season 4: Runner-up
- Season 5: Champion

#### Player Achievements

**Player ID: 6654f375f9eac0fe69961bfd**
- Season 2: 1st in top scorers
- Season 3: 2nd in top scorers

**Player ID: 6617944dd6085929ce96da8c**
- Season 2: 2nd in top scorers

**Player ID: 660a712a0381fc57697ec9fe**
- Season 3: 3rd in top scorers

**Player ID: 660bbdbb22901e293800b2f3**
- Season 2: 1st in top assists
- Season 3: 3rd in top assists

**Player ID: 6624e5ae2e2d3dd16735c1fa**
- Season 3: 1st in top scorers

### Safety Features

- The script checks if achievements already exist before adding them
- It will skip any duplicate achievements
- It automatically fetches league and team information to populate the achievements correctly
- All operations are logged to the console for transparency

### Troubleshooting

If you encounter any issues:

1. **Database Connection Error**: Make sure your `MONGODB_URI` environment variable is set correctly
2. **Player/Team Not Found**: Verify that the IDs in the script match the actual IDs in your database
3. **Permission Errors**: Ensure your database user has write permissions

### Customizing the Script

To add different achievements, edit the `teamAchievements` and `playerAchievements` arrays in `add-achievements.ts`:

```typescript
const teamAchievements = [
  {
    teamId: 'your-team-id-here',
    achievements: [
      { 
        seasonNumber: 1, 
        achievementType: ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER, 
        rank: 1, 
        description: 'Season 1 Champion' 
      }
    ]
  }
];
```

### Available Achievement Types

- `CHAMPIONSHIP_WINNER`
- `FINALIST`
- `THIRD_PLACE`
- `TOP_SCORER`
- `TOP_ASSIST_PROVIDER`
- `BEST_GOALKEEPER`
- `BEST_CENTER_BACK`
- `BEST_DEFENSIVE_MIDFIELDER`
- `BEST_MIDFIELDER`
- `BEST_ATTACKING_MIDFIELDER`
- `BEST_WINGER`
- `BEST_STRIKER`
- `MOST_CLEAN_SHEETS`
- `PLAYER_OF_THE_SEASON`
- `TEAM_OF_THE_SEASON`
