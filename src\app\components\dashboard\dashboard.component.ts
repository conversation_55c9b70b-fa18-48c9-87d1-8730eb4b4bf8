import { Component, OnInit, OnDestroy } from '@angular/core';
import { DashboardService, DashboardSummary } from '../../services/dashboard.service';
import { NotificationService } from '../../services/notification.service';
import { DashboardStateService, DashboardState } from '../../services/state/dashboard-state.service';
import { AuthService } from '../../services/auth.service';
import { PromotionalBannerComponent } from '../promotional-banner/promotional-banner.component';
import { Subscription } from 'rxjs';
import { Router } from '@angular/router';
import { GameService } from '../../services/game.service';
import { GameDTO, GAME_STATUS } from '@pro-clubs-manager/shared-dtos';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss']
})
export class DashboardComponent implements OnInit, OnDestroy {
  dashboardData: DashboardSummary | null = null;
  upcomingGames: GameDTO[] = [];
  isLoading: boolean = true;
  error: string | null = null;
  isAuthenticated: boolean = false;
  canForceRefresh: boolean = true;
  refreshCooldownTime: number = 0;
  promotionalMessage = PromotionalBannerComponent.MESSAGES.DASHBOARD;
  private stateSubscription?: Subscription;
  private authSubscription?: Subscription;

  constructor(
    private dashboardService: DashboardService,
    private notificationService: NotificationService,
    private dashboardStateService: DashboardStateService,
    private authService: AuthService,
    private router: Router,
    private gameService: GameService
  ) { }

  async ngOnInit(): Promise<void> {
    // Subscribe to authentication state
    this.authSubscription = this.authService.isAuthenticated$.subscribe(
      isAuth => this.isAuthenticated = isAuth
    );

    // Subscribe to state changes
    this.stateSubscription = this.dashboardStateService.state$.subscribe(
      (state: DashboardState) => {
        this.isLoading = state.isDashboardLoading;
        this.canForceRefresh = this.dashboardStateService.canForceRefresh();
        this.refreshCooldownTime = this.dashboardStateService.getTimeUntilNextRefresh();

        if (state.dashboardData) {
          // Extract DashboardSummary from cached data (excluding lastUpdated)
          const { lastUpdated, ...dashboardSummary } = state.dashboardData;
          this.dashboardData = dashboardSummary;
        }
      }
    );

    // Load data if not cached or stale
    if (this.dashboardStateService.isDashboardStale()) {
      await this.loadDashboardStats();
    }

    // Load upcoming games for predictions
    await this.loadUpcomingGames();
  }

  ngOnDestroy(): void {
    this.stateSubscription?.unsubscribe();
    this.authSubscription?.unsubscribe();
  }

  private async loadDashboardStats(): Promise<void> {
    try {
      this.dashboardStateService.setDashboardLoading(true);
      this.error = null;

      const data = await this.dashboardService.getDashboardSummary();

      // Update state with new data
      this.dashboardStateService.updateDashboardData({
        ...data,
        lastUpdated: new Date()
      });

    } catch (error) {
      console.error('Error loading dashboard stats:', error);
      this.error = 'Failed to load dashboard statistics';
      this.notificationService.error('Failed to load dashboard data');
      this.dashboardStateService.setDashboardLoading(false);
    }
  }

  async forceRefresh(): Promise<void> {
    if (!this.canForceRefresh) {
      const remainingTime = Math.ceil(this.refreshCooldownTime / 1000);
      this.notificationService.warning(`Please wait ${remainingTime} seconds before refreshing again.`);
      return;
    }

    const refreshAllowed = this.dashboardStateService.forceRefresh();
    if (refreshAllowed) {
      this.notificationService.info('Refreshing dashboard data...');
      await this.loadDashboardStats();
    }
  }

  getCacheInfo(): string {
    const cacheInfo = this.dashboardStateService.getCacheInfo();
    if (cacheInfo.lastUpdate) {
      const timeDiff = Date.now() - cacheInfo.lastUpdate.getTime();
      const minutes = Math.floor(timeDiff / (1000 * 60));
      const hours = Math.floor(minutes / 60);

      if (hours > 0) {
        return `Updated ${hours}h ${minutes % 60}m ago`;
      } else {
        return `Updated ${minutes}m ago`;
      }
    }
    return 'No cache data';
  }

  async refreshDashboard(): Promise<void> {
    this.dashboardStateService.forceRefresh();
    await this.loadDashboardStats();
  }

  getFormattedRating(rating: number | null): string {
    if (rating === null || rating === undefined) return '0.0';
    return rating.toFixed(1);
  }

  getFormattedGoalsPerGame(goalsPerGame: number | null): string {
    if (goalsPerGame === null || goalsPerGame === undefined) return '0.0';
    return goalsPerGame.toFixed(1);
  }

  navigateToPlayerDetails(): void {
    if (!this.dashboardData?.topScorer) {
      return;
    };

    this.router.navigate(['/player-details', { id: this.dashboardData.topScorer.playerId }]);
  }

  navigateToAllSeasons(): void {
    this.router.navigate(['/all-time-stats']);
  }

  navigateToTeamDetails(): void {
    if (!this.dashboardData?.leagueLeader) {
      return;
    };

    this.router.navigate(['/team-details', { id: this.dashboardData.leagueLeader.teamId }]);
  }

  private async loadUpcomingGames(): Promise<void> {
    try {
      // Get upcoming scheduled games for predictions
      const allGames = await this.gameService.getAllGames();
      this.upcomingGames = allGames
        ?.filter((game: GameDTO) => game.status === GAME_STATUS.SCHEDULED)
        ?.slice(0, 6) || []; // Get first 6 upcoming games
    } catch (error) {
      console.error('Error loading upcoming games:', error);
      this.upcomingGames = [];
    }
  }
}