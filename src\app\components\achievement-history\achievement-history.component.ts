import { Component, Input, OnInit } from '@angular/core';
import { SeasonAchievementService, PlayerAchievementHistory, TeamAchievementHistory, ACHIEVEMENT_TYPE } from '../../services/season-achievement.service';
import { NotificationService } from '../../services/notification.service';

export interface AchievementDisplay {
  seasonNumber: number;
  leagueName: string;
  achievementType: string;
  rank?: number;
  description?: string;
  year: number;
  icon: string;
  color: string;
}

@Component({
  selector: 'app-achievement-history',
  templateUrl: './achievement-history.component.html',
  styleUrls: ['./achievement-history.component.scss']
})
export class AchievementHistoryComponent implements OnInit {
  @Input() entityId: string = ''; // Player ID or Team ID
  @Input() entityType: 'player' | 'team' = 'player';
  @Input() entityName: string = '';

  achievements: AchievementDisplay[] = [];
  isLoading: boolean = false;
  groupedAchievements: { [year: number]: AchievementDisplay[] } = {};

  constructor(
    private seasonAchievementService: SeasonAchievementService,
    private notificationService: NotificationService
  ) {}

  async ngOnInit(): Promise<void> {
    if (this.entityId) {
      await this.loadAchievements();
    }
  }

  async loadAchievements(): Promise<void> {
    if (!this.entityId) return;

    this.isLoading = true;
    try {
      let history: PlayerAchievementHistory | TeamAchievementHistory;
      
      if (this.entityType === 'player') {
        history = await this.seasonAchievementService.getPlayerAchievementHistory(this.entityId);
      } else {
        history = await this.seasonAchievementService.getTeamAchievementHistory(this.entityId);
      }

      this.achievements = history.achievements.map(achievement => ({
        ...achievement,
        icon: this.getAchievementIcon(achievement.achievementType),
        color: this.getAchievementColor(achievement.achievementType)
      }));

      this.groupAchievementsByYear();
    } catch (error: any) {
      console.error('Error loading achievement history:', error);

      // More detailed error handling
      if (error?.status === 404) {
        console.log('No achievement history found for this entity');
        this.achievements = [];
        this.groupedAchievements = {};
      } else if (error?.status === 0) {
        console.error('Network error - server may be down');
        this.notificationService.error('Unable to connect to server. Please check your connection.');
      } else {
        const errorMessage = error?.error?.message || error?.message || 'Unknown error occurred';
        console.error('Achievement API error:', errorMessage);
        this.notificationService.error(`Failed to load achievements: ${errorMessage}`);
      }
    } finally {
      this.isLoading = false;
    }
  }

  private groupAchievementsByYear(): void {
    this.groupedAchievements = {};
    
    this.achievements.forEach(achievement => {
      if (!this.groupedAchievements[achievement.year]) {
        this.groupedAchievements[achievement.year] = [];
      }
      this.groupedAchievements[achievement.year].push(achievement);
    });

    // Sort achievements within each year by season number (descending)
    Object.keys(this.groupedAchievements).forEach(year => {
      this.groupedAchievements[parseInt(year)].sort((a, b) => b.seasonNumber - a.seasonNumber);
    });
  }

  getYears(): number[] {
    return Object.keys(this.groupedAchievements)
      .map(year => parseInt(year))
      .sort((a, b) => b - a); // Most recent first
  }

  getAchievementIcon(achievementType: string): string {
    switch (achievementType) {
      case ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER:
        return 'fas fa-trophy';
      case ACHIEVEMENT_TYPE.FINALIST:
        return 'fas fa-medal';
      case ACHIEVEMENT_TYPE.THIRD_PLACE:
        return 'fas fa-award';
      case ACHIEVEMENT_TYPE.TOP_SCORER:
        return 'fas fa-futbol';
      case ACHIEVEMENT_TYPE.TOP_ASSIST_PROVIDER:
        return 'fas fa-hands-helping';
      case ACHIEVEMENT_TYPE.BEST_GOALKEEPER:
        return 'fas fa-hand-paper';
      case ACHIEVEMENT_TYPE.BEST_CENTER_BACK:
      case ACHIEVEMENT_TYPE.BEST_DEFENSIVE_MIDFIELDER:
        return 'fas fa-shield-alt';
      case ACHIEVEMENT_TYPE.BEST_MIDFIELDER:
      case ACHIEVEMENT_TYPE.BEST_ATTACKING_MIDFIELDER:
        return 'fas fa-running';
      case ACHIEVEMENT_TYPE.BEST_WINGER:
        return 'fas fa-arrows-alt-h';
      case ACHIEVEMENT_TYPE.BEST_STRIKER:
        return 'fas fa-crosshairs';
      case ACHIEVEMENT_TYPE.MOST_CLEAN_SHEETS:
        return 'fas fa-shield-alt';
      case ACHIEVEMENT_TYPE.PLAYER_OF_THE_SEASON:
        return 'fas fa-star';
      case ACHIEVEMENT_TYPE.TEAM_OF_THE_SEASON:
        return 'fas fa-users';
      default:
        return 'fas fa-star';
    }
  }

  getAchievementColor(achievementType: string): string {
    switch (achievementType) {
      case ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER:
        return '#ffd700'; // Gold
      case ACHIEVEMENT_TYPE.FINALIST:
        return '#c0c0c0'; // Silver
      case ACHIEVEMENT_TYPE.THIRD_PLACE:
        return '#cd7f32'; // Bronze
      case ACHIEVEMENT_TYPE.TOP_SCORER:
        return '#ff6b35'; // Orange-red
      case ACHIEVEMENT_TYPE.TOP_ASSIST_PROVIDER:
        return '#4ecdc4'; // Teal
      case ACHIEVEMENT_TYPE.BEST_GOALKEEPER:
        return '#9b59b6'; // Purple
      case ACHIEVEMENT_TYPE.BEST_CENTER_BACK:
      case ACHIEVEMENT_TYPE.BEST_DEFENSIVE_MIDFIELDER:
        return '#34495e'; // Dark blue-gray
      case ACHIEVEMENT_TYPE.BEST_MIDFIELDER:
      case ACHIEVEMENT_TYPE.BEST_ATTACKING_MIDFIELDER:
        return '#3498db'; // Blue
      case ACHIEVEMENT_TYPE.BEST_WINGER:
        return '#e74c3c'; // Red
      case ACHIEVEMENT_TYPE.BEST_STRIKER:
        return '#e67e22'; // Orange
      case ACHIEVEMENT_TYPE.MOST_CLEAN_SHEETS:
        return '#27ae60'; // Green
      case ACHIEVEMENT_TYPE.PLAYER_OF_THE_SEASON:
      case ACHIEVEMENT_TYPE.TEAM_OF_THE_SEASON:
        return '#f39c12'; // Yellow-orange
      default:
        return '#95a5a6'; // Gray
    }
  }

  getRankSuffix(rank: number): string {
    if (!rank) return '';
    const suffixes = ["th", "st", "nd", "rd"];
    const v = rank % 100;
    return rank + (suffixes[(v - 20) % 10] || suffixes[v] || suffixes[0]);
  }

  getAchievementTitle(achievement: AchievementDisplay): string {
    let title = achievement.achievementType;

    if (achievement.rank && achievement.rank <= 3) {
      title = `${this.getRankSuffix(achievement.rank)} ${title}`;
    }

    return title;
  }

  getAchievementStats(achievement: AchievementDisplay): string {
    if (achievement.achievementType === ACHIEVEMENT_TYPE.TOP_SCORER && achievement.description) {
      // Extract goals and games from description if available
      const match = achievement.description.match(/(\d+) goals in (\d+) games \(([0-9.]+) per game\)/);
      if (match) {
        return `${match[1]} goals (${match[3]} per game)`;
      }
    }

    if (achievement.achievementType === ACHIEVEMENT_TYPE.TOP_ASSIST_PROVIDER && achievement.description) {
      // Extract assists and games from description if available
      const match = achievement.description.match(/(\d+) assists in (\d+) games \(([0-9.]+) per game\)/);
      if (match) {
        return `${match[1]} assists (${match[3]} per game)`;
      }
    }

    return achievement.description || '';
  }

  getAchievementSubtitle(achievement: AchievementDisplay): string {
    return `Season ${achievement.seasonNumber} • ${achievement.leagueName}`;
  }

  hasAchievements(): boolean {
    return this.achievements.length > 0;
  }

  getAchievementCount(): number {
    return this.achievements.length;
  }

  getUniqueAchievementTypes(): string[] {
    const types = new Set(this.achievements.map(a => a.achievementType));
    return Array.from(types);
  }

  getAchievementsByType(type: string): AchievementDisplay[] {
    return this.achievements.filter(a => a.achievementType === type);
  }

  getMostRecentAchievement(): AchievementDisplay | null {
    if (this.achievements.length === 0) return null;
    
    return this.achievements.reduce((latest, current) => {
      if (current.year > latest.year) return current;
      if (current.year === latest.year && current.seasonNumber > latest.seasonNumber) return current;
      return latest;
    });
  }

  getChampionshipCount(): number {
    return this.achievements.filter(a => a.achievementType === ACHIEVEMENT_TYPE.CHAMPIONSHIP_WINNER).length;
  }

  getTopScorerCount(): number {
    return this.achievements.filter(a => a.achievementType === ACHIEVEMENT_TYPE.TOP_SCORER).length;
  }

  getTopAssistCount(): number {
    return this.achievements.filter(a => a.achievementType === ACHIEVEMENT_TYPE.TOP_ASSIST_PROVIDER).length;
  }
}
