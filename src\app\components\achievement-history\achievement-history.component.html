<div class="achievement-history-container">
  <div class="header">
    <h3>
      <i class="fas fa-trophy"></i>
      Achievement History
    </h3>
    <div class="achievement-stats" *ngIf="hasAchievements()">
      <span class="stat-item">
        <i class="fas fa-award"></i>
        {{ getAchievementCount() }} Total
      </span>
      <span class="stat-item" *ngIf="getChampionshipCount() > 0">
        <i class="fas fa-trophy"></i>
        {{ getChampionshipCount() }} Championships
      </span>
      <span class="stat-item" *ngIf="entityType === 'player' && getTopScorerCount() > 0">
        <i class="fas fa-futbol"></i>
        {{ getTopScorerCount() }} Top Scorer
      </span>
      <span class="stat-item" *ngIf="entityType === 'player' && getTopAssistCount() > 0">
        <i class="fas fa-hands-helping"></i>
        {{ getTopAssistCount() }} Top Assists
      </span>
    </div>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="loading-section">
    <div class="spinner"></div>
    <p>Loading achievement history...</p>
  </div>

  <!-- No Achievements State -->
  <div *ngIf="!isLoading && !hasAchievements()" class="no-achievements">
    <i class="fas fa-medal"></i>
    <h4>No Achievements Yet</h4>
    <p>{{ entityName }} hasn't earned any achievements yet. Keep playing to make history!</p>
  </div>

  <!-- Achievement Timeline -->
  <div *ngIf="!isLoading && hasAchievements()" class="achievement-timeline">
    <div class="year-section" *ngFor="let year of getYears()">
      <div class="year-header">
        <h4>{{ year }}</h4>
        <div class="year-line"></div>
      </div>
      
      <div class="achievements-grid">
        <div 
          class="achievement-card" 
          *ngFor="let achievement of groupedAchievements[year]"
          [style.border-left-color]="achievement.color">
          
          <div class="achievement-icon" [style.background-color]="achievement.color">
            <i [class]="achievement.icon"></i>
          </div>
          
          <div class="achievement-content">
            <div class="achievement-title">
              {{ getAchievementTitle(achievement) }}
            </div>
            <div class="achievement-subtitle">
              {{ getAchievementSubtitle(achievement) }}
            </div>
            <div class="achievement-description" *ngIf="getAchievementStats(achievement)">
              {{ getAchievementStats(achievement) }}
            </div>
          </div>
          
          <div class="achievement-rank" *ngIf="achievement.rank && achievement.rank <= 3">
            <div class="rank-badge" [class]="'rank-' + achievement.rank">
              {{ getRankSuffix(achievement.rank) }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Achievement Summary -->
  <div *ngIf="!isLoading && hasAchievements()" class="achievement-summary">
    <h4>Achievement Summary</h4>
    <div class="summary-grid">
      <div class="summary-item" *ngFor="let type of getUniqueAchievementTypes()">
        <div class="summary-icon" [style.background-color]="getAchievementColor(type)">
          <i [class]="getAchievementIcon(type)"></i>
        </div>
        <div class="summary-content">
          <div class="summary-title">{{ type }}</div>
          <div class="summary-count">{{ getAchievementsByType(type).length }}x</div>
        </div>
      </div>
    </div>
  </div>

  <!-- Most Recent Achievement Highlight -->
  <div *ngIf="!isLoading && hasAchievements() && getMostRecentAchievement()" class="recent-achievement">
    <h4>Most Recent Achievement</h4>
    <div class="recent-card" [style.border-color]="getMostRecentAchievement()!.color">
      <div class="recent-icon" [style.background-color]="getMostRecentAchievement()!.color">
        <i [class]="getMostRecentAchievement()!.icon"></i>
      </div>
      <div class="recent-content">
        <div class="recent-title">
          {{ getAchievementTitle(getMostRecentAchievement()!) }}
        </div>
        <div class="recent-subtitle">
          {{ getAchievementSubtitle(getMostRecentAchievement()!) }}
        </div>
        <div class="recent-description" *ngIf="getAchievementStats(getMostRecentAchievement()!)">
          {{ getAchievementStats(getMostRecentAchievement()!) }}
        </div>
      </div>
    </div>
  </div>
</div>
