import { ClientSession, Types } from "mongoose";
import { inject, injectable, container } from "tsyringe";
import logger from "../config/logger";
import { BadRequestError } from "../errors";
import { IGameRepository, IGameService } from "../interfaces/game";
import { IPlayerService } from "../interfaces/player";
import { ITeamService } from "../interfaces/team";
import { ISeasonCompletionService } from "../interfaces/wrapper-services/season-completion-service.interface";
import { IDashboardService } from "./dashboard-service";
import { CacheService } from "../interfaces/util-services/cache-service.interface";
import { GameMapper } from "../mappers/game-mapper";
import { AddGameData, IGame, PlayerGamePerformance } from "../models/game/game";
import { transactionService } from "./util-services/transaction-service";
import { GameDTO, GAME_STATUS, UpdatePlayerPerformanceDataRequest } from "@pro-clubs-manager/shared-dtos";
import { TopAvgRatingByPosition } from "../repositories/game-repository";
@injectable()
export class GameService implements IGameService {
  private gameRepository: IGameRepository;
  private teamService: ITeamService;
  private playerService: IPlayerService;
  private cacheService: CacheService;
  private seasonCompletionService: ISeasonCompletionService;
  private dashboardService: IDashboardService; // eslint-disable-line @typescript-eslint/no-unused-vars

  constructor(
    @inject("IGameRepository") gameRepository: IGameRepository,
    @inject("ITeamService") teamService: ITeamService,
    @inject("IPlayerService") playerService: IPlayerService,
    @inject("CacheService") cacheService: CacheService,
    @inject("IDashboardService") dashboardService: IDashboardService
  ) {
    this.gameRepository = gameRepository;
    this.teamService = teamService;
    this.playerService = playerService;
    this.cacheService = cacheService;
    this.dashboardService = dashboardService;
    // Lazy load season completion service to avoid circular dependency
    this.seasonCompletionService = null as any;
  }

  private getSeasonCompletionService(): ISeasonCompletionService {
    if (!this.seasonCompletionService) {
      this.seasonCompletionService = container.resolve<ISeasonCompletionService>("ISeasonCompletionService");
    }
    return this.seasonCompletionService;
  }

  async getGamesByIds(gamesIds: Types.ObjectId[]): Promise<GameDTO[]> {
    logger.info(`GameService: fetching ${gamesIds.length} games`);

    const games = await this.gameRepository.getGamesByIds(gamesIds);

    return await GameMapper.mapToDtos(games);
  }

  async getGameById(id: string): Promise<GameDTO> {
    logger.info(`GameService: getting game ${id}`);

    const game = await this.gameRepository.getGameById(id);

    return await GameMapper.mapToDto(game);
  }

  async getAllGames(): Promise<GameDTO[]> {
    logger.info(`GameService: getting all games`);

    const GAMES_CACHE_KEY = "games:all";

    try {
      // Try to get from cache first
      const cachedGames = await this.cacheService.get(GAMES_CACHE_KEY);
      if (cachedGames) {
        logger.info(`GameService: returning ${JSON.parse(cachedGames).length} games from cache`);
        return JSON.parse(cachedGames);
      }

      // If not in cache, fetch from database
      logger.info(`GameService: fetching games from database`);
      const games = await this.gameRepository.getAllGames();
      const gameDTOs = await GameMapper.mapToDtos(games);

      // Cache the results for 30 minutes (games change less frequently than stats)
      await this.cacheService.set(GAMES_CACHE_KEY, gameDTOs, 30 * 60);

      return gameDTOs;
    } catch (error) {
      logger.error("GameService: Error in getAllGames:", error);
      // Fallback to direct database query if cache fails
      const games = await this.gameRepository.getAllGames();
      return await GameMapper.mapToDtos(games);
    }
  }

  async getCurrentSeasonTeamGames(teamId: string, limit?: number): Promise<GameDTO[]> {
    logger.info(`GameService: getting games for team ${teamId}`);

    const team = await this.teamService.getTeamEntityById(teamId);
    if (!team.currentSeason) {
      throw new BadRequestError(`Team with id ${teamId} does not have a current season`);
    }

    const { league, seasonNumber } = team.currentSeason;

    const teamGames = await this.gameRepository.getLeagueSeasonTeamGames(teamId, league, seasonNumber, limit);

    return await GameMapper.mapToDtos(teamGames);
  }

  async getTeamVsTeamHistory(team1Id: string, team2Id: string, limit?: number, session?: ClientSession): Promise<GameDTO[]> {
    logger.info(`GameService: getting team vs team history for teams ${team1Id} and ${team2Id}`);

    const games = await this.gameRepository.getTeamVsTeamHistory(team1Id, team2Id, limit, session);

    return await GameMapper.mapToDtos(games);
  }

  async createGame(fixtureId: Types.ObjectId, leagueId: Types.ObjectId, seasonNumber: number, gameData: AddGameData, session: ClientSession): Promise<GameDTO> {
    logger.info(`GameService: creating game, home team ${gameData.homeTeam} and away team ${gameData.awayTeam}`);

    const game = await this.gameRepository.createGame(fixtureId, leagueId, seasonNumber, gameData, session);

    return await GameMapper.mapToDto(game);
  }

  async createFixtureGames(
    fixtureId: Types.ObjectId,
    leagueId: Types.ObjectId,
    seasonNumber: number,
    gamesData: AddGameData[],
    session: ClientSession
  ): Promise<IGame[]> {
    logger.info(`GameService: creating games for fixture with id ${fixtureId}`);

    return await this.gameRepository.createGames(fixtureId, leagueId, seasonNumber, gamesData, session);
  }

  async setTechnicalResult(gameId: string, losingTeamId: string, reason: string, date: Date): Promise<void> {
    logger.info(`GameService: setting techincal result for game with id ${gameId}`);

    const game = await this.gameRepository.getGameById(gameId);
    const teamId = new Types.ObjectId(losingTeamId);

    if (!game.homeTeam.equals(teamId) && !game.awayTeam.equals(teamId)) {
      throw new BadRequestError("Losing team is not part of this game");
    }

    return await transactionService.withTransaction(async (session) => {
      game.technicalLoss = {
        teamId,
        reason,
      };
      if (game.homeTeam.equals(teamId)) {
        await this.teamService.updateTeamGameStats(game.homeTeam, 0, 3, session);
        await this.teamService.updateTeamGameStats(game.awayTeam, 3, 0, session);
        game.result = { homeTeamGoals: 0, awayTeamGoals: 3 };
      } else {
        await this.teamService.updateTeamGameStats(game.homeTeam, 3, 0, session);
        await this.teamService.updateTeamGameStats(game.awayTeam, 0, 3, session);
        game.result = { homeTeamGoals: 3, awayTeamGoals: 0 };
      }
      game.status = GAME_STATUS.COMPLETED;
      game.date = date;
      await game.save({ session });

      // Update league table cache after technical result
      await this.updateLeagueTableCache(game.league.toString());
    });
  }

  async updateGameResult(gameId: string, homeTeamGoals: number, awayTeamGoals: number, date: Date, isPlayoffGame: boolean, penalties?: { homeTeamPenalties: number; awayTeamPenalties: number }): Promise<void> {
    logger.info(`GameService: updating game ${gameId} result`);
    logger.info(`GameService: received penalties:`, JSON.stringify(penalties));
    logger.info(`GameService: isPlayoffGame:`, isPlayoffGame);

    const game = await this.gameRepository.getGameById(gameId);
    logger.info(`GameService: current game result before update:`, JSON.stringify(game.result));

    return await transactionService.withTransaction(async (session) => {
      if (game.status !== GAME_STATUS.SCHEDULED && !isPlayoffGame) {
        await this.teamService.revertTeamGameStats(game.homeTeam, game.result!.homeTeamGoals, game.result!.awayTeamGoals, session);

        await this.teamService.revertTeamGameStats(game.awayTeam, game.result!.awayTeamGoals, game.result!.homeTeamGoals, session);
      }

      const newResult = {
        homeTeamGoals,
        awayTeamGoals,
        penalties: penalties ? {
          homeTeamPenalties: penalties.homeTeamPenalties,
          awayTeamPenalties: penalties.awayTeamPenalties
        } : undefined,
      };

      logger.info(`GameService: setting new result:`, JSON.stringify(newResult));
      game.result = newResult;

      game.status = GAME_STATUS.PLAYED;
      //game.date = date; i dont want the game's date to be defined by the result update's date

      if (!isPlayoffGame) {
        await this.teamService.updateTeamGameStats(game.homeTeam, homeTeamGoals, awayTeamGoals, session);
        await this.teamService.updateTeamGameStats(game.awayTeam, awayTeamGoals, homeTeamGoals, session);
      }
      await game.save({ session });

      logger.info(`GameService: game saved successfully. Final result in memory:`, JSON.stringify(game.result));

      // Verify what was actually saved to database
      const savedGame = await this.gameRepository.getGameById(gameId, session);
      logger.info(`GameService: verified saved result in database:`, JSON.stringify(savedGame.result));

      // Update league table cache after successful game result update
      await this.updateLeagueTableCache(game.league.toString());

      // Check if season is complete after this game
      try {
        const seasonResult = await this.getSeasonCompletionService().completeSeasonIfFinal(game.league.toString(), session);
        if (seasonResult) {
          logger.info(`Season completed! Champion: ${seasonResult.champion.teamName}`);
        }
      } catch (error) {
        logger.error('Error checking season completion:', error);
        // Don't throw - season completion is not critical for game update
      }
    });
  }

  async updateGameDate(gameId: string, date: Date): Promise<void> {
    logger.info(`GameService: updating game ${gameId} date to ${date}`);

    const game = await this.gameRepository.getGameById(gameId);
    game.date = date;
    await game.save();
  }

  async updateGameBroadcast(gameId: string, streamUrl: string, broadcastingTeam: string): Promise<void> {
    logger.info(`GameService: updating game ${gameId} broadcast info`);

    const game = await this.gameRepository.getGameById(gameId);

    if (streamUrl && broadcastingTeam) {
      game.broadcast = {
        streamUrl,
        broadcastingTeam
      };
    } else {
      game.broadcast = undefined;
    }

    await game.save();
  }

  async updateTeamPlayersPerformance(gameId: string, isHomeTeam: boolean, playersPerformance: UpdatePlayerPerformanceDataRequest[]) {
    logger.info(`GameService: updating game ${gameId} team stats`);
    const game = await this.gameRepository.getGameById(gameId);

    if (game.status !== GAME_STATUS.PLAYED && game.status !== GAME_STATUS.COMPLETED) {
      throw new BadRequestError(`can't update game team stats before updating its result`);
    }

    return await transactionService.withTransaction(async (session) => {
      const isCleanSheet = isHomeTeam ? game.result!.awayTeamGoals === 0 : game.result!.homeTeamGoals === 0;

      // Filter out BOT players before ObjectId conversion
      const realPlayersPerformance = playersPerformance.filter((playerPerformance) => {
        const isBotPlayer = playerPerformance.rating === 0 && (!playerPerformance.playerId || playerPerformance.playerId.trim() === '');
        if (isBotPlayer) {
          logger.info(`Filtering out BOT player with rating ${playerPerformance.rating} and playerId "${playerPerformance.playerId}"`);
        }
        return !isBotPlayer;
      });

      const playersStats: PlayerGamePerformance[] = realPlayersPerformance.map((playerPerformance) => ({
        ...playerPerformance,
        playerId: new Types.ObjectId(playerPerformance.playerId),
        cleanSheet: isCleanSheet,
      }));

      await this.setGamePlayersPerformance(game, isHomeTeam, playersStats, session);

      if (game.homeTeamPlayersPerformance?.length && game.awayTeamPlayersPerformance?.length) {
        game.status = GAME_STATUS.COMPLETED;
      }

      await this.playerService.updatePlayersGamePerformance(playersStats, session);

      await game.save({ session });
    });
  }

  private async setGamePlayersPerformance(game: IGame, isHomeTeam: boolean, playersStats: PlayerGamePerformance[], session: ClientSession) {
    if (isHomeTeam) {
      if (game.homeTeamPlayersPerformance?.length) {
        await this.playerService.revertPlayersGamePerformance(game.homeTeamPlayersPerformance, session);
      }
      game.homeTeamPlayersPerformance = playersStats;
    } else {
      if (game.awayTeamPlayersPerformance?.length) {
        await this.playerService.revertPlayersGamePerformance(game.awayTeamPlayersPerformance, session);
      }
      game.awayTeamPlayersPerformance = playersStats;
    }
  }

  async deleteGame(id: string): Promise<void> {
    logger.info(`deleting game ${id}`);
    await this.gameRepository.deleteGameById(id);
  }

  async getTopAvgRatingByPosition(position: string, minimumGames: number = 1): Promise<TopAvgRatingByPosition[]> {
    logger.info(`GameService: getting top avg rating by position:  ${position}, min games: ${minimumGames}`);

    const topAvgRating = await this.gameRepository.getTopAvgRatingByPosition(position, minimumGames);

    return topAvgRating;

  }

  private async updateLeagueTableCache(leagueId: string): Promise<void> {
    try {
      logger.info(`GameService: clearing all relevant caches for league ${leagueId}`);

      // Clear all caches that are affected by game result changes
      const cacheKeys = [
        `leagueTable:${leagueId}`,      // League table standings
        `topScorers:${leagueId}`,       // Top scorers list
        `topAssists:${leagueId}`,       // Top assists list
        "dashboard:summary",            // Dashboard summary with top scorer and league leader
        "games:all"                     // All games list
      ];

      // Clear all caches in parallel
      await Promise.all(cacheKeys.map(key => this.cacheService.delete(key)));

      logger.info(`GameService: all relevant caches cleared for league ${leagueId}`);
    } catch (error) {
      logger.error(`GameService: error clearing caches for league ${leagueId}:`, error);
      // Don't throw - cache update is not critical for game update
    }
  }
}
