import { Router } from "express";
import { container } from "../config/container.config";
import SeasonAchievementController from "../controllers/season-achievement-controller";
import { authenticateToken, requireAdmin } from "../middlewares/auth-middleware";

const router = Router();
const seasonAchievementController = container.resolve(SeasonAchievementController);

// Admin-only routes for season management
router.get("/preview/:leagueId/:seasonNumber?", authenticateToken, requireAdmin, (req, res, next) => 
  seasonAchievementController.previewSeasonEnd(req, res, next)
);

router.post("/end-season", authenticateToken, requireAdmin, (req, res, next) => 
  seasonAchievementController.endSeason(req, res, next)
);

router.get("/can-end/:leagueId/:seasonNumber", authenticateToken, requireAdmin, (req, res, next) => 
  seasonAchievementController.canEndSeason(req, res, next)
);

// Public routes for viewing achievements
router.get("/league/:leagueId", (req, res, next) => 
  seasonAchievementController.getLeagueSeasonAchievements(req, res, next)
);

router.get("/league/:leagueId/season/:seasonNumber", (req, res, next) => 
  seasonAchievementController.getSeasonAchievements(req, res, next)
);

router.get("/player/:playerId/history", (req, res, next) => 
  seasonAchievementController.getPlayerAchievementHistory(req, res, next)
);

router.get("/team/:teamId/history", (req, res, next) => 
  seasonAchievementController.getTeamAchievementHistory(req, res, next)
);

router.get("/top-performers/:leagueId/:seasonNumber", (req, res, next) => 
  seasonAchievementController.getSeasonTopPerformers(req, res, next)
);

router.get("/championship/:leagueId/:seasonNumber", (req, res, next) => 
  seasonAchievementController.getChampionshipResults(req, res, next)
);

export default router;
