import { Request, Response, NextFunction } from "express";
import { injectable, inject } from "tsyringe";
import { ISeasonAchievementService } from "../interfaces/wrapper-services/season-achievement-service.interface";
import logger from "../config/logger";

@injectable()
export default class SeasonAchievementController {
  constructor(
    @inject("ISeasonAchievementService") private seasonAchievementService: ISeasonAchievementService
  ) {}

  /**
   * Preview what achievements would be recorded if season ended now
   * GET /season-achievements/preview/:leagueId/:seasonNumber?
   */
  async previewSeasonEnd(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { leagueId, seasonNumber } = req.params;
      
      logger.info(`SeasonAchievementController: Previewing season end for league ${leagueId}`);

      const preview = await this.seasonAchievementService.previewSeasonEnd(
        leagueId,
        seasonNumber ? parseInt(seasonNumber) : undefined
      );

      res.status(200).json({
        success: true,
        data: preview
      });
    } catch (error: any) {
      logger.error(`Error previewing season end: ${error.message}`);
      next(error);
    }
  }

  /**
   * Manually end a season and record all achievements
   * POST /season-achievements/end-season
   */
  async endSeason(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { leagueId, seasonNumber, endDate, forceEnd } = req.body;
      const adminUserId = req.userId; // From auth middleware

      if (!leagueId || !seasonNumber) {
        res.status(400).json({
          success: false,
          message: "League ID and season number are required"
        });
        return;
      }

      if (!adminUserId) {
        res.status(401).json({
          success: false,
          message: "Admin authentication required"
        });
        return;
      }

      logger.info(`SeasonAchievementController: Ending season ${seasonNumber} for league ${leagueId} by admin ${adminUserId}`);

      const result = await this.seasonAchievementService.endSeason(
        {
          leagueId,
          seasonNumber: parseInt(seasonNumber),
          endDate: endDate ? new Date(endDate) : undefined,
          forceEnd: Boolean(forceEnd)
        },
        adminUserId
      );

      res.status(200).json({
        success: true,
        data: result,
        message: result.message
      });
    } catch (error: any) {
      logger.error(`Error ending season: ${error.message}`);
      
      if (error.message.includes("already been recorded") || error.message.includes("cannot be ended")) {
        res.status(400).json({
          success: false,
          message: error.message
        });
        return;
      }
      
      next(error);
    }
  }

  /**
   * Get all season achievements for a league
   * GET /season-achievements/league/:leagueId
   */
  async getLeagueSeasonAchievements(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { leagueId } = req.params;

      logger.info(`SeasonAchievementController: Getting season achievements for league ${leagueId}`);

      const achievements = await this.seasonAchievementService.getLeagueSeasonAchievements(leagueId);

      res.status(200).json({
        success: true,
        data: achievements
      });
    } catch (error: any) {
      logger.error(`Error getting league season achievements: ${error.message}`);
      next(error);
    }
  }

  /**
   * Get season achievements for a specific season
   * GET /season-achievements/league/:leagueId/season/:seasonNumber
   */
  async getSeasonAchievements(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { leagueId, seasonNumber } = req.params;

      logger.info(`SeasonAchievementController: Getting achievements for league ${leagueId} season ${seasonNumber}`);

      const achievements = await this.seasonAchievementService.getSeasonAchievements(
        leagueId,
        parseInt(seasonNumber)
      );

      if (!achievements) {
        res.status(404).json({
          success: false,
          message: "Season achievements not found"
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: achievements
      });
    } catch (error: any) {
      logger.error(`Error getting season achievements: ${error.message}`);
      next(error);
    }
  }

  /**
   * Get achievement history for a player
   * GET /season-achievements/player/:playerId/history
   */
  async getPlayerAchievementHistory(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { playerId } = req.params;

      logger.info(`SeasonAchievementController: Getting achievement history for player ${playerId}`);

      const history = await this.seasonAchievementService.getPlayerAchievementHistory(playerId);

      res.status(200).json({
        success: true,
        data: history
      });
    } catch (error: any) {
      logger.error(`Error getting player achievement history: ${error.message}`);
      next(error);
    }
  }

  /**
   * Get achievement history for a team
   * GET /season-achievements/team/:teamId/history
   */
  async getTeamAchievementHistory(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { teamId } = req.params;

      logger.info(`SeasonAchievementController: Getting achievement history for team ${teamId}`);

      const history = await this.seasonAchievementService.getTeamAchievementHistory(teamId);

      res.status(200).json({
        success: true,
        data: history
      });
    } catch (error: any) {
      logger.error(`Error getting team achievement history: ${error.message}`);
      next(error);
    }
  }

  /**
   * Check if a season can be ended
   * GET /season-achievements/can-end/:leagueId/:seasonNumber
   */
  async canEndSeason(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { leagueId, seasonNumber } = req.params;

      logger.info(`SeasonAchievementController: Checking if season ${seasonNumber} can be ended for league ${leagueId}`);

      const result = await this.seasonAchievementService.canEndSeason(
        leagueId,
        parseInt(seasonNumber)
      );

      res.status(200).json({
        success: true,
        data: result
      });
    } catch (error: any) {
      logger.error(`Error checking if season can be ended: ${error.message}`);
      next(error);
    }
  }

  /**
   * Get top performers for a season
   * GET /season-achievements/top-performers/:leagueId/:seasonNumber
   */
  async getSeasonTopPerformers(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { leagueId, seasonNumber } = req.params;

      logger.info(`SeasonAchievementController: Getting top performers for league ${leagueId} season ${seasonNumber}`);

      const performers = await this.seasonAchievementService.calculateSeasonTopPerformers(
        leagueId,
        parseInt(seasonNumber)
      );

      res.status(200).json({
        success: true,
        data: performers
      });
    } catch (error: any) {
      logger.error(`Error getting season top performers: ${error.message}`);
      next(error);
    }
  }

  /**
   * Get championship results for a season
   * GET /season-achievements/championship/:leagueId/:seasonNumber
   */
  async getChampionshipResults(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { leagueId, seasonNumber } = req.params;

      logger.info(`SeasonAchievementController: Getting championship results for league ${leagueId} season ${seasonNumber}`);

      const results = await this.seasonAchievementService.calculateChampionshipResults(
        leagueId,
        parseInt(seasonNumber)
      );

      res.status(200).json({
        success: true,
        data: results
      });
    } catch (error: any) {
      logger.error(`Error getting championship results: ${error.message}`);
      next(error);
    }
  }
}
