/* === ALL-TIME STATS COMPONENT === */

.all-time-stats-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-lg);
  background: var(--surface-primary);
  min-height: 100vh;
}

/* === PAGE HEADER === */
.page-header {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-xl);
  background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
  border-radius: var(--radius-xl);
  color: white;
  box-shadow: var(--shadow-lg);

  .page-title {
    font-size: var(--text-3xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-md);

    i {
      font-size: var(--text-2xl);
    }
  }

  .page-description {
    font-size: var(--text-lg);
    opacity: 0.9;
    margin: 0;
  }
}

/* === LEAGUE SELECTION === */
.league-selection {
  margin-bottom: var(--spacing-xl);
  
  .form-group {
    max-width: 400px;
    margin: 0 auto;

    .form-label {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      font-weight: var(--font-weight-semibold);
      color: var(--text-primary);
      margin-bottom: var(--spacing-sm);
      font-size: var(--text-base);
    }

    .form-select {
      width: 100%;
      padding: var(--spacing-md);
      border: 2px solid var(--border-primary);
      border-radius: var(--radius-lg);
      background: var(--surface-secondary);
      color: var(--text-primary);
      font-size: var(--text-base);
      transition: all 0.2s ease;

      &:focus {
        outline: none;
        border-color: var(--primary-500);
        box-shadow: 0 0 0 3px rgba(var(--primary-500-rgb), 0.1);
      }
    }
  }
}

/* === LEAGUE INFO === */
.league-info {
  text-align: center;
  margin-bottom: var(--spacing-xl);

  .league-title {
    font-size: var(--text-2xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
  }

  .league-subtitle {
    font-size: var(--text-lg);
    color: var(--text-secondary); // Use secondary for subtitles
    margin: 0;
  }
}

/* === TAB NAVIGATION === */
.tab-navigation {
  display: flex;
  justify-content: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
  
  .tab-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--surface-secondary);
    border: 2px solid var(--border-primary);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    font-weight: var(--font-weight-semibold);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: var(--surface-tertiary);
      border-color: var(--primary-300);
      color: var(--text-primary);
    }

    &.active {
      background: var(--primary-500);
      border-color: var(--primary-500);
      color: white;
      box-shadow: var(--shadow-md);
    }

    .tab-count {
      background: rgba(255, 255, 255, 0.2);
      padding: 2px 8px;
      border-radius: var(--radius-full);
      font-size: var(--text-sm);
    }
  }

  @media (max-width: 768px) {
    flex-direction: column;
    
    .tab-btn {
      justify-content: center;
    }
  }
}

/* === STATS TABLES === */
.stats-table {
  background: var(--surface-secondary);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-primary);

  .table-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);

    .table-title {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-sm);
      font-size: var(--text-xl);
      font-weight: var(--font-weight-bold);
      color: var(--text-primary);
      margin-bottom: var(--spacing-sm);
    }

    .table-subtitle {
      color: var(--text-secondary); // Use secondary for subtitles to maintain hierarchy
      margin: 0;
    }
  }
}

/* === PLAYERS LIST === */
.players-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.player-row {
  display: grid;
  grid-template-columns: auto 1fr auto auto;
  gap: var(--spacing-lg);
  align-items: center;
  padding: var(--spacing-lg);
  background: var(--surface-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-300);
  }

  &.top-three {
    background: linear-gradient(135deg, var(--warning-50), var(--warning-100));
    border-color: var(--warning-300);

    &:hover {
      background: linear-gradient(135deg, var(--warning-100), var(--warning-200));
    }

    // Ensure all text is properly visible on light warning background
    .rank {
      color: var(--text-primary) !important;
    }

    .player-name {
      color: var(--text-primary) !important;
    }

    .player-position {
      color: var(--text-secondary) !important;
    }

    .team-name {
      color: var(--text-secondary) !important;
    }

    .stat-value {
      color: var(--text-primary) !important;
    }

    .stat-label {
      color: var(--text-secondary) !important;
    }

    .primary-stat .stat-value {
      color: var(--primary-600) !important; // Keep primary color for main stat
    }
  }

  .rank-section {
    .rank {
      font-size: var(--text-xl);
      font-weight: var(--font-weight-bold);
      color: var(--text-primary);
      min-width: 40px;
      text-align: center;
    }
  }

  .player-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);

    .player-avatar {
      width: 50px;
      height: 50px;
      border-radius: var(--radius-full);
      object-fit: cover;
      border: 2px solid var(--border-primary);
    }

    .player-details {
      .player-name {
        font-size: var(--text-lg);
        font-weight: var(--font-weight-bold);
        color: var(--text-primary);
        margin: 0 0 var(--spacing-xs) 0;
      }

      .player-position {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        margin: 0;
      }
    }
  }

  .team-info {
    cursor: pointer;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--surface-tertiary);
    border-radius: var(--radius-md);
    transition: all 0.2s ease;

    &:hover {
      background: var(--primary-100);
      color: var(--primary-700);
    }

    .team-name {
      font-size: var(--text-sm);
      font-weight: var(--font-weight-medium);
      color: var(--text-primary); // Changed from text-secondary for better contrast
    }
  }

  .stats-section {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);

    .primary-stat {
      text-align: center;

      .stat-value {
        display: block;
        font-size: var(--text-2xl);
        font-weight: var(--font-weight-bold);
        color: var(--primary-500);
        line-height: 1;
      }

      .stat-label {
        font-size: var(--text-sm);
        color: var(--text-secondary);
        font-weight: var(--font-weight-medium);
      }
    }

    .secondary-stats {
      display: flex;
      gap: var(--spacing-md);

      .stat-item {
        text-align: center;

        .stat-value {
          display: block;
          font-size: var(--text-base);
          font-weight: var(--font-weight-semibold);
          color: var(--text-primary);
          line-height: 1;
        }

        .stat-label {
          font-size: var(--text-xs);
          color: var(--text-secondary);
        }
      }
    }
  }

  @media (max-width: 768px) {
    grid-template-columns: auto 1fr;
    gap: var(--spacing-md);

    .team-info,
    .stats-section .secondary-stats {
      display: none;
    }

    .stats-section {
      gap: var(--spacing-sm);
    }
  }
}

/* === LOADING & EMPTY STATES === */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);

  i {
    font-size: var(--text-2xl);
    margin-bottom: var(--spacing-md);
    color: var(--primary-500);
  }

  p {
    font-size: var(--text-lg);
    margin: 0;
  }
}

.no-data {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-secondary);

  i {
    font-size: var(--text-3xl);
    margin-bottom: var(--spacing-md);
    color: var(--text-tertiary);
  }

  p {
    font-size: var(--text-lg);
    margin: 0;
  }
}
